import '../models/user.dart';
import '../models/subject.dart';
import '../models/attendance.dart';
import '../services/user_service.dart';
import '../services/subject_service.dart';
import '../services/attendance_service.dart';

class DataSeeder {
  final UserService _userService = UserService();
  final SubjectService _subjectService = SubjectService();
  final AttendanceService _attendanceService = AttendanceService();

  Future<void> seedSampleData() async {
    try {
      // Create sample teacher
      final teacherResult = await _userService.registerUser(
        name: 'Dr. <PERSON>',
        email: '<EMAIL>',
        password: 'teacher123',
        role: UserRole.teacher,
      );

      if (!teacherResult.isSuccess || teacherResult.user == null) {
        print('Teacher already exists or failed to create');
        return;
      }

      final teacher = teacherResult.user!;

      // Create sample students
      final students = <User>[];
      for (int i = 1; i <= 5; i++) {
        final studentResult = await _userService.registerUser(
          name: 'Student $i',
          email: 'student$<EMAIL>',
          password: 'student123',
          role: UserRole.student,
        );

        if (studentResult.isSuccess && studentResult.user != null) {
          students.add(studentResult.user!);
        }
      }

      // Create sample subjects
      final subjects = <Subject>[];
      final subjectNames = ['Mathematics', 'Physics', 'Chemistry'];
      final subjectCodes = ['MATH101', 'PHYS101', 'CHEM101'];

      for (int i = 0; i < subjectNames.length; i++) {
        final subjectResult = await _subjectService.createSubject(
          name: subjectNames[i],
          code: subjectCodes[i],
          teacherId: teacher.id!,
          description: 'Introduction to ${subjectNames[i]}',
        );

        if (subjectResult.isSuccess && subjectResult.subject != null) {
          subjects.add(subjectResult.subject!);
        }
      }

      // Enroll students in subjects
      for (final student in students) {
        for (final subject in subjects) {
          await _subjectService.enrollStudent(student.id!, subject.id!);
        }
      }

      // Create sample attendance records
      final now = DateTime.now();
      for (int dayOffset = 0; dayOffset < 10; dayOffset++) {
        final date = now.subtract(Duration(days: dayOffset));
        
        for (final subject in subjects) {
          for (final student in students) {
            // Random attendance (80% present)
            final isPresent = (student.id! + subject.id! + dayOffset) % 5 != 0;
            
            await _attendanceService.markAttendance(
              studentId: student.id!,
              subjectId: subject.id!,
              date: date,
              status: isPresent ? AttendanceStatus.present : AttendanceStatus.absent,
              markedBy: teacher.id!,
            );
          }
        }
      }

      print('Sample data seeded successfully!');
    } catch (e) {
      print('Error seeding data: $e');
    }
  }
}
