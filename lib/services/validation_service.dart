class ValidationService {
  // Email validation
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    
    // Basic email regex pattern
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    
    return emailRegex.hasMatch(email.trim());
  }

  // Password validation
  static PasswordValidationResult validatePassword(String password) {
    final errors = <String>[];
    
    if (password.isEmpty) {
      errors.add('Password is required');
      return PasswordValidationResult(isValid: false, errors: errors);
    }
    
    if (password.length < 6) {
      errors.add('Password must be at least 6 characters long');
    }
    
    if (password.length > 128) {
      errors.add('Password must be less than 128 characters');
    }
    
    // Check for at least one letter
    if (!RegExp(r'[a-zA-Z]').hasMatch(password)) {
      errors.add('Password must contain at least one letter');
    }
    
    // Check for at least one number (optional but recommended)
    if (!RegExp(r'[0-9]').hasMatch(password)) {
      // This is a warning, not an error for basic validation
      // errors.add('Password should contain at least one number');
    }
    
    // Check for common weak passwords
    final commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', '111111', '123123', 'admin', 'letmein'
    ];
    
    if (commonPasswords.contains(password.toLowerCase())) {
      errors.add('Password is too common. Please choose a stronger password');
    }
    
    return PasswordValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  // Name validation
  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'Name is required';
    }
    
    final trimmedName = name.trim();
    
    if (trimmedName.length < 2) {
      return 'Name must be at least 2 characters long';
    }
    
    if (trimmedName.length > 50) {
      return 'Name must be less than 50 characters';
    }
    
    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    if (!RegExp(r"^[a-zA-Z\s\-']+$").hasMatch(trimmedName)) {
      return 'Name can only contain letters, spaces, hyphens, and apostrophes';
    }
    
    return null;
  }

  // Email validation for forms
  static String? validateEmailField(String? email) {
    if (email == null || email.trim().isEmpty) {
      return 'Email is required';
    }
    
    if (!isValidEmail(email)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  // Password confirmation validation
  static String? validatePasswordConfirmation(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'Please confirm your password';
    }
    
    if (password != confirmPassword) {
      return 'Passwords do not match';
    }
    
    return null;
  }

  // General text field validation
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  // Phone number validation (optional for future use)
  static String? validatePhoneNumber(String? phone) {
    if (phone == null || phone.trim().isEmpty) {
      return null; // Phone is optional
    }
    
    // Remove all non-digit characters
    final digitsOnly = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    if (digitsOnly.length < 10 || digitsOnly.length > 15) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }

  // Student ID validation (for future use)
  static String? validateStudentId(String? studentId) {
    if (studentId == null || studentId.trim().isEmpty) {
      return 'Student ID is required';
    }
    
    final trimmedId = studentId.trim();
    
    if (trimmedId.length < 3 || trimmedId.length > 20) {
      return 'Student ID must be between 3 and 20 characters';
    }
    
    // Allow alphanumeric characters and hyphens
    if (!RegExp(r'^[a-zA-Z0-9\-]+$').hasMatch(trimmedId)) {
      return 'Student ID can only contain letters, numbers, and hyphens';
    }
    
    return null;
  }

  // Subject code validation (for future use)
  static String? validateSubjectCode(String? code) {
    if (code == null || code.trim().isEmpty) {
      return 'Subject code is required';
    }
    
    final trimmedCode = code.trim().toUpperCase();
    
    if (trimmedCode.length < 2 || trimmedCode.length > 10) {
      return 'Subject code must be between 2 and 10 characters';
    }
    
    // Allow alphanumeric characters only
    if (!RegExp(r'^[A-Z0-9]+$').hasMatch(trimmedCode)) {
      return 'Subject code can only contain letters and numbers';
    }
    
    return null;
  }

  // Check password strength (returns a score from 0-4)
  static int getPasswordStrength(String password) {
    if (password.isEmpty) return 0;
    
    int score = 0;
    
    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Character variety checks
    if (RegExp(r'[a-z]').hasMatch(password)) score++;
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;
    if (RegExp(r'[0-9]').hasMatch(password)) score++;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;
    
    // Reduce score for common patterns
    if (RegExp(r'(.)\1{2,}').hasMatch(password)) score--; // Repeated characters
    if (RegExp(r'(012|123|234|345|456|567|678|789|890)').hasMatch(password)) score--; // Sequential numbers
    if (RegExp(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)').hasMatch(password.toLowerCase())) score--; // Sequential letters
    
    return score.clamp(0, 4);
  }

  // Get password strength description
  static String getPasswordStrengthDescription(int strength) {
    switch (strength) {
      case 0:
        return 'Very Weak';
      case 1:
        return 'Weak';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Strong';
      default:
        return 'Unknown';
    }
  }

  // Get password strength color
  static String getPasswordStrengthColor(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return 'red';
      case 2:
        return 'orange';
      case 3:
        return 'yellow';
      case 4:
        return 'green';
      default:
        return 'grey';
    }
  }
}

class PasswordValidationResult {
  final bool isValid;
  final List<String> errors;

  PasswordValidationResult({
    required this.isValid,
    required this.errors,
  });

  String get errorMessage => errors.join('\n');
}
