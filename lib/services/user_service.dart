import 'dart:convert';
import 'package:crypto/crypto.dart';
import '../models/user.dart';
import 'database_helper.dart';

class UserService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Hash password using SHA-256
  String _hashPassword(String password) {
    var bytes = utf8.encode(password);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Register a new user
  Future<UserRegistrationResult> registerUser({
    required String name,
    required String email,
    required String password,
    required UserRole role,
  }) async {
    try {
      // Validate input
      if (name.trim().isEmpty) {
        return UserRegistrationResult.failure('Name cannot be empty');
      }

      if (email.trim().isEmpty || !_isValidEmail(email)) {
        return UserRegistrationResult.failure('Please enter a valid email address');
      }

      if (password.length < 6) {
        return UserRegistrationResult.failure('Password must be at least 6 characters long');
      }

      // Check if email already exists
      if (await _databaseHelper.emailExists(email.trim().toLowerCase())) {
        return UserRegistrationResult.failure('Email already exists');
      }

      // Create new user with hashed password
      final user = User(
        name: name.trim(),
        email: email.trim().toLowerCase(),
        password: _hashPassword(password),
        role: role,
      );

      // Insert user into database
      final userId = await _databaseHelper.insertUser(user);
      final createdUser = user.copyWith(id: userId);

      return UserRegistrationResult.success(createdUser);
    } catch (e) {
      return UserRegistrationResult.failure('Registration failed: ${e.toString()}');
    }
  }

  // Login user
  Future<UserLoginResult> loginUser({
    required String email,
    required String password,
  }) async {
    try {
      // Validate input
      if (email.trim().isEmpty || password.isEmpty) {
        return UserLoginResult.failure('Email and password are required');
      }

      // Get user by email
      final user = await _databaseHelper.getUserByEmail(email.trim().toLowerCase());
      
      if (user == null) {
        return UserLoginResult.failure('Invalid email or password');
      }

      // Verify password
      final hashedPassword = _hashPassword(password);
      if (user.password != hashedPassword) {
        return UserLoginResult.failure('Invalid email or password');
      }

      return UserLoginResult.success(user);
    } catch (e) {
      return UserLoginResult.failure('Login failed: ${e.toString()}');
    }
  }

  // Get user by ID
  Future<User?> getUserById(int id) async {
    try {
      return await _databaseHelper.getUserById(id);
    } catch (e) {
      print('Error getting user by ID: ${e.toString()}');
      return null;
    }
  }

  // Get all users by role
  Future<List<User>> getUsersByRole(UserRole role) async {
    try {
      return await _databaseHelper.getUsersByRole(role);
    } catch (e) {
      print('Error getting users by role: ${e.toString()}');
      return [];
    }
  }

  // Update user profile
  Future<bool> updateUser(User user) async {
    try {
      final result = await _databaseHelper.updateUser(user);
      return result > 0;
    } catch (e) {
      print('Error updating user: ${e.toString()}');
      return false;
    }
  }

  // Change password
  Future<bool> changePassword({
    required int userId,
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // Get current user
      final user = await _databaseHelper.getUserById(userId);
      if (user == null) {
        return false;
      }

      // Verify current password
      final hashedCurrentPassword = _hashPassword(currentPassword);
      if (user.password != hashedCurrentPassword) {
        return false;
      }

      // Validate new password
      if (newPassword.length < 6) {
        return false;
      }

      // Update password
      final updatedUser = user.copyWith(password: _hashPassword(newPassword));
      final result = await _databaseHelper.updateUser(updatedUser);
      return result > 0;
    } catch (e) {
      print('Error changing password: ${e.toString()}');
      return false;
    }
  }

  // Delete user
  Future<bool> deleteUser(int userId) async {
    try {
      final result = await _databaseHelper.deleteUser(userId);
      return result > 0;
    } catch (e) {
      print('Error deleting user: ${e.toString()}');
      return false;
    }
  }

  // Email validation
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Get all users (for admin purposes)
  Future<List<User>> getAllUsers() async {
    try {
      return await _databaseHelper.getAllUsers();
    } catch (e) {
      print('Error getting all users: ${e.toString()}');
      return [];
    }
  }
}

// Result classes for better error handling
class UserRegistrationResult {
  final bool isSuccess;
  final User? user;
  final String? errorMessage;

  UserRegistrationResult._({
    required this.isSuccess,
    this.user,
    this.errorMessage,
  });

  factory UserRegistrationResult.success(User user) {
    return UserRegistrationResult._(isSuccess: true, user: user);
  }

  factory UserRegistrationResult.failure(String errorMessage) {
    return UserRegistrationResult._(isSuccess: false, errorMessage: errorMessage);
  }
}

class UserLoginResult {
  final bool isSuccess;
  final User? user;
  final String? errorMessage;

  UserLoginResult._({
    required this.isSuccess,
    this.user,
    this.errorMessage,
  });

  factory UserLoginResult.success(User user) {
    return UserLoginResult._(isSuccess: true, user: user);
  }

  factory UserLoginResult.failure(String errorMessage) {
    return UserLoginResult._(isSuccess: false, errorMessage: errorMessage);
  }
}
