import '../models/subject.dart';
import '../models/user.dart';
import 'database_helper.dart';

class SubjectService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Create a new subject
  Future<SubjectResult> createSubject({
    required String name,
    required String code,
    required int teacherId,
    String? description,
  }) async {
    try {
      // Validate input
      if (name.trim().isEmpty) {
        return SubjectResult.failure('Subject name cannot be empty');
      }

      if (code.trim().isEmpty) {
        return SubjectResult.failure('Subject code cannot be empty');
      }

      // Check if subject code already exists
      final existingSubjects = await _databaseHelper.getAllSubjects();
      final codeExists = existingSubjects.any(
        (subject) => subject.code.toLowerCase() == code.trim().toLowerCase(),
      );

      if (codeExists) {
        return SubjectResult.failure('Subject code already exists');
      }

      // Create new subject
      final subject = Subject(
        name: name.trim(),
        code: code.trim().toUpperCase(),
        teacherId: teacherId,
        description: description?.trim(),
        createdAt: DateTime.now(),
      );

      // Insert subject into database
      final subjectId = await _databaseHelper.insertSubject(subject);
      final createdSubject = subject.copyWith(id: subjectId);

      return SubjectResult.success(createdSubject);
    } catch (e) {
      return SubjectResult.failure('Failed to create subject: ${e.toString()}');
    }
  }

  // Get all subjects
  Future<List<Subject>> getAllSubjects() async {
    try {
      return await _databaseHelper.getAllSubjects();
    } catch (e) {
      print('Error getting all subjects: ${e.toString()}');
      return [];
    }
  }

  // Get subjects by teacher
  Future<List<Subject>> getSubjectsByTeacher(int teacherId) async {
    try {
      return await _databaseHelper.getSubjectsByTeacher(teacherId);
    } catch (e) {
      print('Error getting subjects by teacher: ${e.toString()}');
      return [];
    }
  }

  // Get subjects by student (enrolled subjects)
  Future<List<Subject>> getSubjectsByStudent(int studentId) async {
    try {
      return await _databaseHelper.getSubjectsByStudent(studentId);
    } catch (e) {
      print('Error getting subjects by student: ${e.toString()}');
      return [];
    }
  }

  // Update subject
  Future<bool> updateSubject(Subject subject) async {
    try {
      final result = await _databaseHelper.updateSubject(subject);
      return result > 0;
    } catch (e) {
      print('Error updating subject: ${e.toString()}');
      return false;
    }
  }

  // Delete subject
  Future<bool> deleteSubject(int subjectId) async {
    try {
      final result = await _databaseHelper.deleteSubject(subjectId);
      return result > 0;
    } catch (e) {
      print('Error deleting subject: ${e.toString()}');
      return false;
    }
  }

  // Enroll student in subject
  Future<bool> enrollStudent(int studentId, int subjectId) async {
    try {
      // Check if student is already enrolled
      final enrollments = await _databaseHelper.getStudentEnrollments(studentId);
      final alreadyEnrolled = enrollments.any(
        (enrollment) => enrollment.subjectId == subjectId,
      );

      if (alreadyEnrolled) {
        return false; // Already enrolled
      }

      final result = await _databaseHelper.enrollStudentInSubject(studentId, subjectId);
      return result > 0;
    } catch (e) {
      print('Error enrolling student: ${e.toString()}');
      return false;
    }
  }

  // Unenroll student from subject
  Future<bool> unenrollStudent(int studentId, int subjectId) async {
    try {
      final result = await _databaseHelper.unenrollStudentFromSubject(studentId, subjectId);
      return result > 0;
    } catch (e) {
      print('Error unenrolling student: ${e.toString()}');
      return false;
    }
  }

  // Get student enrollments
  Future<List<StudentSubject>> getStudentEnrollments(int studentId) async {
    try {
      return await _databaseHelper.getStudentEnrollments(studentId);
    } catch (e) {
      print('Error getting student enrollments: ${e.toString()}');
      return [];
    }
  }

  // Get subject enrollments (students enrolled in a subject)
  Future<List<StudentSubject>> getSubjectEnrollments(int subjectId) async {
    try {
      return await _databaseHelper.getSubjectEnrollments(subjectId);
    } catch (e) {
      print('Error getting subject enrollments: ${e.toString()}');
      return [];
    }
  }

  // Get available subjects for student enrollment
  Future<List<Subject>> getAvailableSubjectsForStudent(int studentId) async {
    try {
      final allSubjects = await _databaseHelper.getAllSubjects();
      final enrolledSubjects = await _databaseHelper.getSubjectsByStudent(studentId);
      
      // Filter out already enrolled subjects
      final enrolledSubjectIds = enrolledSubjects.map((s) => s.id).toSet();
      return allSubjects.where((subject) => !enrolledSubjectIds.contains(subject.id)).toList();
    } catch (e) {
      print('Error getting available subjects: ${e.toString()}');
      return [];
    }
  }

  // Search subjects by name or code
  Future<List<Subject>> searchSubjects(String query) async {
    try {
      final allSubjects = await _databaseHelper.getAllSubjects();
      final lowercaseQuery = query.toLowerCase();
      
      return allSubjects.where((subject) {
        return subject.name.toLowerCase().contains(lowercaseQuery) ||
               subject.code.toLowerCase().contains(lowercaseQuery) ||
               (subject.teacherName?.toLowerCase().contains(lowercaseQuery) ?? false);
      }).toList();
    } catch (e) {
      print('Error searching subjects: ${e.toString()}');
      return [];
    }
  }

  // Validate subject code format
  bool isValidSubjectCode(String code) {
    // Subject code should be 2-10 characters, alphanumeric
    if (code.length < 2 || code.length > 10) return false;
    return RegExp(r'^[A-Z0-9]+$').hasMatch(code.toUpperCase());
  }

  // Validate subject name
  bool isValidSubjectName(String name) {
    // Subject name should be 2-100 characters
    if (name.trim().length < 2 || name.trim().length > 100) return false;
    return true;
  }
}

// Result classes for better error handling
class SubjectResult {
  final bool isSuccess;
  final Subject? subject;
  final String? errorMessage;

  SubjectResult._({
    required this.isSuccess,
    this.subject,
    this.errorMessage,
  });

  factory SubjectResult.success(Subject subject) {
    return SubjectResult._(isSuccess: true, subject: subject);
  }

  factory SubjectResult.failure(String errorMessage) {
    return SubjectResult._(isSuccess: false, errorMessage: errorMessage);
  }
}
