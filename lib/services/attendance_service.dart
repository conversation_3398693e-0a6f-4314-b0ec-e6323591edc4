import '../models/attendance.dart';
import '../models/subject.dart';
import 'database_helper.dart';

class AttendanceService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Mark attendance for a student
  Future<AttendanceResult> markAttendance({
    required int studentId,
    required int subjectId,
    required DateTime date,
    required AttendanceStatus status,
    required int markedBy,
  }) async {
    try {
      // Check if attendance already exists for this student, subject, and date
      final existingAttendance = await _databaseHelper.getAttendanceRecord(
        studentId,
        subjectId,
        date,
      );

      if (existingAttendance != null) {
        // Update existing attendance
        final updatedAttendance = existingAttendance.copyWith(
          status: status,
          markedBy: markedBy,
        );

        final result = await _databaseHelper.updateAttendance(
          updatedAttendance,
        );
        if (result > 0) {
          return AttendanceResult.success(updatedAttendance);
        } else {
          return AttendanceResult.failure('Failed to update attendance');
        }
      } else {
        // Create new attendance record
        final attendance = Attendance(
          studentId: studentId,
          subjectId: subjectId,
          date: date,
          status: status,
          markedBy: markedBy,
          createdAt: DateTime.now(),
        );

        final attendanceId = await _databaseHelper.insertAttendance(attendance);
        final createdAttendance = attendance.copyWith(id: attendanceId);

        return AttendanceResult.success(createdAttendance);
      }
    } catch (e) {
      return AttendanceResult.failure(
        'Failed to mark attendance: ${e.toString()}',
      );
    }
  }

  // Mark attendance for multiple students
  Future<List<AttendanceResult>> markBulkAttendance({
    required List<int> studentIds,
    required int subjectId,
    required DateTime date,
    required Map<int, AttendanceStatus> attendanceMap,
    required int markedBy,
  }) async {
    final results = <AttendanceResult>[];

    for (final studentId in studentIds) {
      final status = attendanceMap[studentId] ?? AttendanceStatus.absent;
      final result = await markAttendance(
        studentId: studentId,
        subjectId: subjectId,
        date: date,
        status: status,
        markedBy: markedBy,
      );
      results.add(result);
    }

    return results;
  }

  // Get attendance records for a student
  Future<List<Attendance>> getStudentAttendance(int studentId) async {
    try {
      return await _databaseHelper.getAttendanceByStudent(studentId);
    } catch (e) {
      print('Error getting student attendance: ${e.toString()}');
      return [];
    }
  }

  // Get attendance records for a subject
  Future<List<Attendance>> getSubjectAttendance(int subjectId) async {
    try {
      return await _databaseHelper.getAttendanceBySubject(subjectId);
    } catch (e) {
      print('Error getting subject attendance: ${e.toString()}');
      return [];
    }
  }

  // Get attendance records for a specific date
  Future<List<Attendance>> getAttendanceByDate(DateTime date) async {
    try {
      return await _databaseHelper.getAttendanceByDate(date);
    } catch (e) {
      print('Error getting attendance by date: ${e.toString()}');
      return [];
    }
  }

  // Get attendance record for specific student, subject, and date
  Future<Attendance?> getAttendanceRecord(
    int studentId,
    int subjectId,
    DateTime date,
  ) async {
    try {
      return await _databaseHelper.getAttendanceRecord(
        studentId,
        subjectId,
        date,
      );
    } catch (e) {
      print('Error getting attendance record: ${e.toString()}');
      return null;
    }
  }

  // Get attendance statistics for a student
  Future<List<AttendanceStats>> getStudentAttendanceStats(int studentId) async {
    try {
      return await _databaseHelper.getAttendanceStatsByStudent(studentId);
    } catch (e) {
      print('Error getting student attendance stats: ${e.toString()}');
      return [];
    }
  }

  // Get attendance statistics for a subject
  Future<List<AttendanceStats>> getSubjectAttendanceStats(int subjectId) async {
    try {
      return await _databaseHelper.getAttendanceStatsBySubject(subjectId);
    } catch (e) {
      print('Error getting subject attendance stats: ${e.toString()}');
      return [];
    }
  }

  // Get attendance percentage for a student in a specific subject
  Future<double> getAttendancePercentage(int studentId, int subjectId) async {
    try {
      final attendanceRecords = await _databaseHelper.getAttendanceByStudent(
        studentId,
      );
      final subjectAttendance =
          attendanceRecords
              .where((record) => record.subjectId == subjectId)
              .toList();

      if (subjectAttendance.isEmpty) return 0.0;

      final presentCount =
          subjectAttendance
              .where((record) => record.status == AttendanceStatus.present)
              .length;

      return (presentCount / subjectAttendance.length) * 100;
    } catch (e) {
      print('Error calculating attendance percentage: ${e.toString()}');
      return 0.0;
    }
  }

  // Get overall attendance percentage for a student
  Future<double> getOverallAttendancePercentage(int studentId) async {
    try {
      final attendanceRecords = await _databaseHelper.getAttendanceByStudent(
        studentId,
      );

      if (attendanceRecords.isEmpty) return 0.0;

      final presentCount =
          attendanceRecords
              .where((record) => record.status == AttendanceStatus.present)
              .length;

      return (presentCount / attendanceRecords.length) * 100;
    } catch (e) {
      print('Error calculating overall attendance percentage: ${e.toString()}');
      return 0.0;
    }
  }

  // Get attendance summary for a date range
  Future<AttendanceSummary> getAttendanceSummary({
    required int studentId,
    required DateTime startDate,
    required DateTime endDate,
    int? subjectId,
  }) async {
    try {
      final attendanceRecords = await _databaseHelper.getAttendanceByStudent(
        studentId,
      );

      // Filter by date range and subject if specified
      final filteredRecords =
          attendanceRecords.where((record) {
            final recordDate = record.date;
            final inDateRange =
                recordDate.isAfter(
                  startDate.subtract(const Duration(days: 1)),
                ) &&
                recordDate.isBefore(endDate.add(const Duration(days: 1)));
            final matchesSubject =
                subjectId == null || record.subjectId == subjectId;
            return inDateRange && matchesSubject;
          }).toList();

      final totalClasses = filteredRecords.length;
      final presentCount =
          filteredRecords
              .where((record) => record.status == AttendanceStatus.present)
              .length;
      final absentCount = totalClasses - presentCount;
      final percentage =
          totalClasses > 0 ? (presentCount / totalClasses) * 100 : 0.0;

      return AttendanceSummary(
        totalClasses: totalClasses,
        presentCount: presentCount,
        absentCount: absentCount,
        attendancePercentage: percentage,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      print('Error getting attendance summary: ${e.toString()}');
      return AttendanceSummary(
        totalClasses: 0,
        presentCount: 0,
        absentCount: 0,
        attendancePercentage: 0.0,
        startDate: startDate,
        endDate: endDate,
      );
    }
  }

  // Delete attendance record
  Future<bool> deleteAttendance(int attendanceId) async {
    try {
      final result = await _databaseHelper.deleteAttendance(attendanceId);
      return result > 0;
    } catch (e) {
      print('Error deleting attendance: ${e.toString()}');
      return false;
    }
  }

  // Check if attendance can be marked for a date (not future date)
  bool canMarkAttendance(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);

    // Can mark attendance for today and past dates, but not future dates
    return !targetDate.isAfter(today);
  }

  // Get attendance trends for a student (last 30 days)
  Future<List<AttendanceTrend>> getAttendanceTrends(int studentId) async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 30));

      final attendanceRecords = await _databaseHelper.getAttendanceByStudent(
        studentId,
      );

      // Group by date and calculate daily attendance
      final trends = <AttendanceTrend>[];
      for (int i = 0; i < 30; i++) {
        final date = startDate.add(Duration(days: i));
        final dayRecords =
            attendanceRecords.where((record) {
              return record.date.year == date.year &&
                  record.date.month == date.month &&
                  record.date.day == date.day;
            }).toList();

        final totalClasses = dayRecords.length;
        final presentCount =
            dayRecords
                .where((record) => record.status == AttendanceStatus.present)
                .length;

        trends.add(
          AttendanceTrend(
            date: date,
            totalClasses: totalClasses,
            presentCount: presentCount,
            attendancePercentage:
                totalClasses > 0 ? (presentCount / totalClasses) * 100 : 0.0,
          ),
        );
      }

      return trends;
    } catch (e) {
      print('Error getting attendance trends: ${e.toString()}');
      return [];
    }
  }

  // Get attendance records for a subject within a date range
  Future<List<Attendance>> getAttendanceBySubjectAndDateRange(
    int subjectId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final attendanceRecords = await _databaseHelper.getAttendanceBySubject(
        subjectId,
      );

      // Filter by date range
      final filteredRecords =
          attendanceRecords.where((record) {
            final recordDate = record.date;
            return recordDate.isAfter(
                  startDate.subtract(const Duration(days: 1)),
                ) &&
                recordDate.isBefore(endDate);
          }).toList();

      return filteredRecords;
    } catch (e) {
      print(
        'Error getting attendance by subject and date range: ${e.toString()}',
      );
      return [];
    }
  }

  // Get attendance records for a specific subject and date
  Future<List<Attendance>> getAttendanceBySubjectAndDate(
    int subjectId,
    DateTime date,
  ) async {
    try {
      final startDate = DateTime(date.year, date.month, date.day);
      final endDate = startDate.add(const Duration(days: 1));
      return await getAttendanceBySubjectAndDateRange(
        subjectId,
        startDate,
        endDate,
      );
    } catch (e) {
      print('Error getting attendance by subject and date: ${e.toString()}');
      return [];
    }
  }
}

// Result classes for better error handling
class AttendanceResult {
  final bool isSuccess;
  final Attendance? attendance;
  final String? errorMessage;

  AttendanceResult._({
    required this.isSuccess,
    this.attendance,
    this.errorMessage,
  });

  factory AttendanceResult.success(Attendance attendance) {
    return AttendanceResult._(isSuccess: true, attendance: attendance);
  }

  factory AttendanceResult.failure(String errorMessage) {
    return AttendanceResult._(isSuccess: false, errorMessage: errorMessage);
  }
}

class AttendanceSummary {
  final int totalClasses;
  final int presentCount;
  final int absentCount;
  final double attendancePercentage;
  final DateTime startDate;
  final DateTime endDate;

  AttendanceSummary({
    required this.totalClasses,
    required this.presentCount,
    required this.absentCount,
    required this.attendancePercentage,
    required this.startDate,
    required this.endDate,
  });
}

class AttendanceTrend {
  final DateTime date;
  final int totalClasses;
  final int presentCount;
  final double attendancePercentage;

  AttendanceTrend({
    required this.date,
    required this.totalClasses,
    required this.presentCount,
    required this.attendancePercentage,
  });
}
