import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user.dart';
import '../models/subject.dart';
import '../models/attendance.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'college_attendance.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create users table
    await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('student', 'teacher'))
      )
    ''');

    // Create subjects table (for future use)
    await db.execute('''
      CREATE TABLE subjects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT UNIQUE NOT NULL,
        teacher_id INTEGER,
        description TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (teacher_id) REFERENCES users (id)
      )
    ''');

    // Create attendance table (for future use)
    await db.execute('''
      CREATE TABLE attendance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL,
        subject_id INTEGER NOT NULL,
        date TEXT NOT NULL,
        status TEXT NOT NULL CHECK (status IN ('present', 'absent')),
        marked_by INTEGER NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users (id),
        FOREIGN KEY (subject_id) REFERENCES subjects (id),
        FOREIGN KEY (marked_by) REFERENCES users (id),
        UNIQUE(student_id, subject_id, date)
      )
    ''');

    // Create student_subjects table (for future use - many-to-many relationship)
    await db.execute('''
      CREATE TABLE student_subjects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL,
        subject_id INTEGER NOT NULL,
        enrolled_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users (id),
        FOREIGN KEY (subject_id) REFERENCES subjects (id),
        UNIQUE(student_id, subject_id)
      )
    ''');

    print('Database tables created successfully');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic here when needed
      print('Database upgraded from version $oldVersion to $newVersion');
    }
  }

  // User operations
  Future<int> insertUser(User user) async {
    final db = await database;
    return await db.insert('users', user.toMap());
  }

  Future<User?> getUserByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<User?> getUserById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<List<User>> getAllUsers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('users');
    return List.generate(maps.length, (i) => User.fromMap(maps[i]));
  }

  Future<List<User>> getUsersByRole(UserRole role) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'role = ?',
      whereArgs: [role.toString().split('.').last],
    );
    return List.generate(maps.length, (i) => User.fromMap(maps[i]));
  }

  Future<int> updateUser(User user) async {
    final db = await database;
    return await db.update(
      'users',
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Future<int> deleteUser(int id) async {
    final db = await database;
    return await db.delete('users', where: 'id = ?', whereArgs: [id]);
  }

  // Check if email already exists
  Future<bool> emailExists(String email) async {
    final user = await getUserByEmail(email);
    return user != null;
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // Delete database (for testing purposes)
  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), 'college_attendance.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }

  // Subject operations
  Future<int> insertSubject(Subject subject) async {
    final db = await database;
    return await db.insert('subjects', subject.toMap());
  }

  Future<List<Subject>> getAllSubjects() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT s.*, u.name as teacher_name
      FROM subjects s
      LEFT JOIN users u ON s.teacher_id = u.id
    ''');
    return List.generate(maps.length, (i) => Subject.fromMap(maps[i]));
  }

  Future<List<Subject>> getSubjectsByTeacher(int teacherId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT s.*, u.name as teacher_name
      FROM subjects s
      LEFT JOIN users u ON s.teacher_id = u.id
      WHERE s.teacher_id = ?
    ''',
      [teacherId],
    );
    return List.generate(maps.length, (i) => Subject.fromMap(maps[i]));
  }

  Future<List<Subject>> getSubjectsByStudent(int studentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT s.*, u.name as teacher_name
      FROM subjects s
      LEFT JOIN users u ON s.teacher_id = u.id
      INNER JOIN student_subjects ss ON s.id = ss.subject_id
      WHERE ss.student_id = ?
    ''',
      [studentId],
    );
    return List.generate(maps.length, (i) => Subject.fromMap(maps[i]));
  }

  Future<int> updateSubject(Subject subject) async {
    final db = await database;
    return await db.update(
      'subjects',
      subject.toMap(),
      where: 'id = ?',
      whereArgs: [subject.id],
    );
  }

  Future<int> deleteSubject(int id) async {
    final db = await database;
    return await db.delete('subjects', where: 'id = ?', whereArgs: [id]);
  }

  // Student-Subject enrollment operations
  Future<int> enrollStudentInSubject(int studentId, int subjectId) async {
    final db = await database;
    return await db.insert('student_subjects', {
      'student_id': studentId,
      'subject_id': subjectId,
      'enrolled_at': DateTime.now().toIso8601String(),
    });
  }

  Future<int> unenrollStudentFromSubject(int studentId, int subjectId) async {
    final db = await database;
    return await db.delete(
      'student_subjects',
      where: 'student_id = ? AND subject_id = ?',
      whereArgs: [studentId, subjectId],
    );
  }

  Future<List<StudentSubject>> getStudentEnrollments(int studentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT ss.*, u.name as student_name, s.name as subject_name, s.code as subject_code
      FROM student_subjects ss
      LEFT JOIN users u ON ss.student_id = u.id
      LEFT JOIN subjects s ON ss.subject_id = s.id
      WHERE ss.student_id = ?
    ''',
      [studentId],
    );
    return List.generate(maps.length, (i) => StudentSubject.fromMap(maps[i]));
  }

  Future<List<StudentSubject>> getSubjectEnrollments(int subjectId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT ss.*, u.name as student_name, s.name as subject_name, s.code as subject_code
      FROM student_subjects ss
      LEFT JOIN users u ON ss.student_id = u.id
      LEFT JOIN subjects s ON ss.subject_id = s.id
      WHERE ss.subject_id = ?
    ''',
      [subjectId],
    );
    return List.generate(maps.length, (i) => StudentSubject.fromMap(maps[i]));
  }

  // Attendance operations
  Future<int> insertAttendance(Attendance attendance) async {
    final db = await database;
    return await db.insert('attendance', attendance.toMap());
  }

  Future<List<Attendance>> getAttendanceByStudent(int studentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT a.*, u.name as student_name, s.name as subject_name, s.code as subject_code,
             t.name as marked_by_name
      FROM attendance a
      LEFT JOIN users u ON a.student_id = u.id
      LEFT JOIN subjects s ON a.subject_id = s.id
      LEFT JOIN users t ON a.marked_by = t.id
      WHERE a.student_id = ?
      ORDER BY a.date DESC
    ''',
      [studentId],
    );
    return List.generate(maps.length, (i) => Attendance.fromMap(maps[i]));
  }

  Future<List<Attendance>> getAttendanceBySubject(int subjectId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT a.*, u.name as student_name, s.name as subject_name, s.code as subject_code,
             t.name as marked_by_name
      FROM attendance a
      LEFT JOIN users u ON a.student_id = u.id
      LEFT JOIN subjects s ON a.subject_id = s.id
      LEFT JOIN users t ON a.marked_by = t.id
      WHERE a.subject_id = ?
      ORDER BY a.date DESC, u.name ASC
    ''',
      [subjectId],
    );
    return List.generate(maps.length, (i) => Attendance.fromMap(maps[i]));
  }

  Future<List<Attendance>> getAttendanceByDate(DateTime date) async {
    final db = await database;
    final dateStr = date.toIso8601String().split('T')[0];
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT a.*, u.name as student_name, s.name as subject_name, s.code as subject_code,
             t.name as marked_by_name
      FROM attendance a
      LEFT JOIN users u ON a.student_id = u.id
      LEFT JOIN subjects s ON a.subject_id = s.id
      LEFT JOIN users t ON a.marked_by = t.id
      WHERE a.date = ?
      ORDER BY s.name ASC, u.name ASC
    ''',
      [dateStr],
    );
    return List.generate(maps.length, (i) => Attendance.fromMap(maps[i]));
  }

  Future<Attendance?> getAttendanceRecord(
    int studentId,
    int subjectId,
    DateTime date,
  ) async {
    final db = await database;
    final dateStr = date.toIso8601String().split('T')[0];
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT a.*, u.name as student_name, s.name as subject_name, s.code as subject_code,
             t.name as marked_by_name
      FROM attendance a
      LEFT JOIN users u ON a.student_id = u.id
      LEFT JOIN subjects s ON a.subject_id = s.id
      LEFT JOIN users t ON a.marked_by = t.id
      WHERE a.student_id = ? AND a.subject_id = ? AND a.date = ?
    ''',
      [studentId, subjectId, dateStr],
    );

    if (maps.isNotEmpty) {
      return Attendance.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateAttendance(Attendance attendance) async {
    final db = await database;
    return await db.update(
      'attendance',
      attendance.toMap(),
      where: 'id = ?',
      whereArgs: [attendance.id],
    );
  }

  Future<int> deleteAttendance(int id) async {
    final db = await database;
    return await db.delete('attendance', where: 'id = ?', whereArgs: [id]);
  }

  // Attendance statistics
  Future<List<AttendanceStats>> getAttendanceStatsByStudent(
    int studentId,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT
        s.name as subject_name,
        s.code as subject_code,
        COUNT(a.id) as total_classes,
        SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_count,
        SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent_count
      FROM student_subjects ss
      LEFT JOIN subjects s ON ss.subject_id = s.id
      LEFT JOIN attendance a ON ss.subject_id = a.subject_id AND ss.student_id = a.student_id
      WHERE ss.student_id = ?
      GROUP BY s.id, s.name, s.code
    ''',
      [studentId],
    );
    return List.generate(maps.length, (i) => AttendanceStats.fromMap(maps[i]));
  }

  Future<List<AttendanceStats>> getAttendanceStatsBySubject(
    int subjectId,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT
        s.name as subject_name,
        s.code as subject_code,
        COUNT(a.id) as total_classes,
        SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_count,
        SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent_count
      FROM subjects s
      LEFT JOIN attendance a ON s.id = a.subject_id
      WHERE s.id = ?
      GROUP BY s.id, s.name, s.code
    ''',
      [subjectId],
    );
    return List.generate(maps.length, (i) => AttendanceStats.fromMap(maps[i]));
  }
}
