class Subject {
  final int? id;
  final String name;
  final String code;
  final int? teacherId;
  final String? teacherName;
  final String? description;
  final DateTime? createdAt;

  Subject({
    this.id,
    required this.name,
    required this.code,
    this.teacherId,
    this.teacherName,
    this.description,
    this.createdAt,
  });

  // Convert Subject object to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'teacher_id': teacherId,
      'description': description,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // Create Subject object from Map (database result)
  factory Subject.fromMap(Map<String, dynamic> map) {
    return Subject(
      id: map['id'],
      name: map['name'],
      code: map['code'],
      teacherId: map['teacher_id'],
      teacherName: map['teacher_name'], // From JOIN query
      description: map['description'],
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'])
          : null,
    );
  }

  // Create a copy of Subject with updated fields
  Subject copyWith({
    int? id,
    String? name,
    String? code,
    int? teacherId,
    String? teacherName,
    String? description,
    DateTime? createdAt,
  }) {
    return Subject(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      teacherId: teacherId ?? this.teacherId,
      teacherName: teacherName ?? this.teacherName,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Subject{id: $id, name: $name, code: $code, teacherId: $teacherId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Subject &&
        other.id == id &&
        other.name == name &&
        other.code == code &&
        other.teacherId == teacherId;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        code.hashCode ^
        teacherId.hashCode;
  }
}

class StudentSubject {
  final int? id;
  final int studentId;
  final int subjectId;
  final DateTime? enrolledAt;
  final String? studentName;
  final String? subjectName;
  final String? subjectCode;

  StudentSubject({
    this.id,
    required this.studentId,
    required this.subjectId,
    this.enrolledAt,
    this.studentName,
    this.subjectName,
    this.subjectCode,
  });

  // Convert StudentSubject object to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'student_id': studentId,
      'subject_id': subjectId,
      'enrolled_at': enrolledAt?.toIso8601String(),
    };
  }

  // Create StudentSubject object from Map (database result)
  factory StudentSubject.fromMap(Map<String, dynamic> map) {
    return StudentSubject(
      id: map['id'],
      studentId: map['student_id'],
      subjectId: map['subject_id'],
      enrolledAt: map['enrolled_at'] != null 
          ? DateTime.parse(map['enrolled_at'])
          : null,
      studentName: map['student_name'], // From JOIN query
      subjectName: map['subject_name'], // From JOIN query
      subjectCode: map['subject_code'], // From JOIN query
    );
  }

  @override
  String toString() {
    return 'StudentSubject{id: $id, studentId: $studentId, subjectId: $subjectId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StudentSubject &&
        other.id == id &&
        other.studentId == studentId &&
        other.subjectId == subjectId;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        studentId.hashCode ^
        subjectId.hashCode;
  }
}
