enum AttendanceStatus {
  present,
  absent,
}

extension AttendanceStatusExtension on AttendanceStatus {
  String get displayName {
    switch (this) {
      case AttendanceStatus.present:
        return 'Present';
      case AttendanceStatus.absent:
        return 'Absent';
    }
  }

  String get value {
    switch (this) {
      case AttendanceStatus.present:
        return 'present';
      case AttendanceStatus.absent:
        return 'absent';
    }
  }
}

class Attendance {
  final int? id;
  final int studentId;
  final int subjectId;
  final DateTime date;
  final AttendanceStatus status;
  final int markedBy;
  final DateTime? createdAt;
  final String? studentName;
  final String? subjectName;
  final String? subjectCode;
  final String? markedByName;

  Attendance({
    this.id,
    required this.studentId,
    required this.subjectId,
    required this.date,
    required this.status,
    required this.markedBy,
    this.createdAt,
    this.studentName,
    this.subjectName,
    this.subjectCode,
    this.markedByName,
  });

  // Convert Attendance object to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'student_id': studentId,
      'subject_id': subjectId,
      'date': date.toIso8601String().split('T')[0], // Store only date part
      'status': status.value,
      'marked_by': markedBy,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // Create Attendance object from Map (database result)
  factory Attendance.fromMap(Map<String, dynamic> map) {
    return Attendance(
      id: map['id'],
      studentId: map['student_id'],
      subjectId: map['subject_id'],
      date: DateTime.parse(map['date']),
      status: AttendanceStatus.values.firstWhere(
        (e) => e.value == map['status'],
      ),
      markedBy: map['marked_by'],
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'])
          : null,
      studentName: map['student_name'], // From JOIN query
      subjectName: map['subject_name'], // From JOIN query
      subjectCode: map['subject_code'], // From JOIN query
      markedByName: map['marked_by_name'], // From JOIN query
    );
  }

  // Create a copy of Attendance with updated fields
  Attendance copyWith({
    int? id,
    int? studentId,
    int? subjectId,
    DateTime? date,
    AttendanceStatus? status,
    int? markedBy,
    DateTime? createdAt,
    String? studentName,
    String? subjectName,
    String? subjectCode,
    String? markedByName,
  }) {
    return Attendance(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      subjectId: subjectId ?? this.subjectId,
      date: date ?? this.date,
      status: status ?? this.status,
      markedBy: markedBy ?? this.markedBy,
      createdAt: createdAt ?? this.createdAt,
      studentName: studentName ?? this.studentName,
      subjectName: subjectName ?? this.subjectName,
      subjectCode: subjectCode ?? this.subjectCode,
      markedByName: markedByName ?? this.markedByName,
    );
  }

  @override
  String toString() {
    return 'Attendance{id: $id, studentId: $studentId, subjectId: $subjectId, date: $date, status: $status}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Attendance &&
        other.id == id &&
        other.studentId == studentId &&
        other.subjectId == subjectId &&
        other.date == date &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        studentId.hashCode ^
        subjectId.hashCode ^
        date.hashCode ^
        status.hashCode;
  }
}

class AttendanceStats {
  final int totalClasses;
  final int presentCount;
  final int absentCount;
  final double attendancePercentage;
  final String subjectName;
  final String subjectCode;

  AttendanceStats({
    required this.totalClasses,
    required this.presentCount,
    required this.absentCount,
    required this.attendancePercentage,
    required this.subjectName,
    required this.subjectCode,
  });

  factory AttendanceStats.fromMap(Map<String, dynamic> map) {
    final total = map['total_classes'] ?? 0;
    final present = map['present_count'] ?? 0;
    final absent = map['absent_count'] ?? 0;
    final percentage = total > 0 ? (present / total) * 100 : 0.0;

    return AttendanceStats(
      totalClasses: total,
      presentCount: present,
      absentCount: absent,
      attendancePercentage: percentage,
      subjectName: map['subject_name'] ?? '',
      subjectCode: map['subject_code'] ?? '',
    );
  }

  @override
  String toString() {
    return 'AttendanceStats{totalClasses: $totalClasses, presentCount: $presentCount, attendancePercentage: $attendancePercentage}';
  }
}
