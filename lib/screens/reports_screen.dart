import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../models/attendance.dart';
import '../models/subject.dart';
import '../services/attendance_service.dart';
import '../services/subject_service.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final AttendanceService _attendanceService = AttendanceService();
  final SubjectService _subjectService = SubjectService();

  List<Subject> _teacherSubjects = [];
  Subject? _selectedSubject;
  List<AttendanceStats> _subjectStats = [];
  List<Attendance> _recentAttendance = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReportsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReportsData() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final teacherId = authProvider.currentUser?.id;

    if (teacherId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final subjects = await _subjectService.getSubjectsByTeacher(teacherId);
      setState(() {
        _teacherSubjects = subjects;
        if (subjects.isNotEmpty && _selectedSubject == null) {
          _selectedSubject = subjects.first;
          _loadSubjectData();
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading reports data: $e')),
        );
      }
    }
  }

  Future<void> _loadSubjectData() async {
    if (_selectedSubject == null) return;

    try {
      final stats = await _attendanceService.getSubjectAttendanceStats(_selectedSubject!.id!);
      final attendance = await _attendanceService.getSubjectAttendance(_selectedSubject!.id!);

      setState(() {
        _subjectStats = stats;
        _recentAttendance = attendance.take(20).toList();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading subject data: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Reports & Analytics',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _teacherSubjects.isEmpty
              ? _buildNoSubjectsView()
              : Column(
                  children: [
                    // Subject Selector
                    Container(
                      padding: const EdgeInsets.all(16),
                      color: Colors.white,
                      child: DropdownButtonFormField<Subject>(
                        value: _selectedSubject,
                        decoration: InputDecoration(
                          labelText: 'Select Subject',
                          prefixIcon: const Icon(Icons.book_outlined),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: _teacherSubjects.map((subject) {
                          return DropdownMenuItem<Subject>(
                            value: subject,
                            child: Text('${subject.name} (${subject.code})'),
                          );
                        }).toList(),
                        onChanged: (Subject? value) {
                          if (value != null) {
                            setState(() {
                              _selectedSubject = value;
                            });
                            _loadSubjectData();
                          }
                        },
                      ),
                    ),

                    // Tab Bar
                    Container(
                      color: Colors.grey[50],
                      child: TabBar(
                        controller: _tabController,
                        labelColor: Colors.green[600],
                        unselectedLabelColor: Colors.grey[600],
                        indicatorColor: Colors.green[600],
                        tabs: const [
                          Tab(text: 'Overview', icon: Icon(Icons.dashboard_outlined)),
                          Tab(text: 'Statistics', icon: Icon(Icons.bar_chart_outlined)),
                          Tab(text: 'Records', icon: Icon(Icons.list_alt_outlined)),
                        ],
                      ),
                    ),

                    // Tab Views
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildOverviewTab(),
                          _buildStatisticsTab(),
                          _buildRecordsTab(),
                        ],
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildNoSubjectsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Subjects Assigned',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Contact admin to assign subjects to view reports',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    if (_selectedSubject == null) {
      return const Center(child: Text('No subject selected'));
    }

    // Calculate overall stats
    final totalStudents = _recentAttendance.map((a) => a.studentId).toSet().length;
    final totalClasses = _recentAttendance.map((a) => a.date).toSet().length;
    final totalPresent = _recentAttendance.where((a) => a.status == AttendanceStatus.present).length;
    final overallPercentage = totalClasses > 0 ? (totalPresent / _recentAttendance.length) * 100 : 0.0;

    return RefreshIndicator(
      onRefresh: _loadSubjectData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Subject Info Card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: [Colors.green[600]!, Colors.green[400]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedSubject!.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Code: ${_selectedSubject!.code}',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Overall Attendance: ${overallPercentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Quick Stats
            Text(
              'Quick Statistics',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Students',
                    totalStudents.toString(),
                    Icons.people_outlined,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Classes Held',
                    totalClasses.toString(),
                    Icons.calendar_today_outlined,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Present',
                    totalPresent.toString(),
                    Icons.check_circle_outline,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Total Absent',
                    (_recentAttendance.length - totalPresent).toString(),
                    Icons.cancel_outlined,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Recent Activity
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),

            if (_recentAttendance.isEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Text(
                    'No attendance records found',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
              )
            else
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: _recentAttendance
                      .take(5)
                      .map((attendance) => _buildActivityItem(attendance))
                      .toList(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(Attendance attendance) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: attendance.status == AttendanceStatus.present
            ? Colors.green.withOpacity(0.1)
            : Colors.red.withOpacity(0.1),
        child: Icon(
          attendance.status == AttendanceStatus.present
              ? Icons.check
              : Icons.close,
          color: attendance.status == AttendanceStatus.present
              ? Colors.green
              : Colors.red,
        ),
      ),
      title: Text(
        attendance.studentName ?? 'Unknown Student',
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        '${_formatDate(attendance.date)} • ${attendance.status.displayName}',
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: attendance.status == AttendanceStatus.present
              ? Colors.green.withOpacity(0.1)
              : Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          attendance.status.displayName,
          style: TextStyle(
            color: attendance.status == AttendanceStatus.present
                ? Colors.green
                : Colors.red,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsTab() {
    return RefreshIndicator(
      onRefresh: _loadSubjectData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Detailed Analytics',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),

            if (_subjectStats.isEmpty)
              const Center(
                child: Text('No statistics available'),
              )
            else
              ..._subjectStats.map((stat) => _buildDetailedStatCard(stat)),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordsTab() {
    return RefreshIndicator(
      onRefresh: _loadSubjectData,
      child: _recentAttendance.isEmpty
          ? const Center(
              child: Text('No attendance records available'),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: _recentAttendance.length,
              itemBuilder: (context, index) {
                final attendance = _recentAttendance[index];
                return _buildRecordItem(attendance);
              },
            ),
    );
  }

  Widget _buildDetailedStatCard(AttendanceStats stat) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              stat.subjectName,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Progress Bar
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: stat.attendancePercentage / 100,
                child: Container(
                  decoration: BoxDecoration(
                    color: _getAttendanceColor(stat.attendancePercentage),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Stats Row
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total',
                    stat.totalClasses.toString(),
                    Icons.calendar_today_outlined,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Present',
                    stat.presentCount.toString(),
                    Icons.check_circle_outline,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Absent',
                    stat.absentCount.toString(),
                    Icons.cancel_outlined,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Percentage',
                    '${stat.attendancePercentage.toStringAsFixed(1)}%',
                    Icons.trending_up,
                    _getAttendanceColor(stat.attendancePercentage),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
