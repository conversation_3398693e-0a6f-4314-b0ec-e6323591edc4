import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../models/attendance.dart';
import '../models/subject.dart';
import '../models/user.dart';
import '../services/attendance_service.dart';
import '../services/subject_service.dart';
import '../services/user_service.dart';

class TeacherAttendanceView extends StatefulWidget {
  const TeacherAttendanceView({super.key});

  @override
  State<TeacherAttendanceView> createState() => _TeacherAttendanceViewState();
}

class _TeacherAttendanceViewState extends State<TeacherAttendanceView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final AttendanceService _attendanceService = AttendanceService();
  final SubjectService _subjectService = SubjectService();
  final UserService _userService = UserService();

  List<Subject> _teacherSubjects = [];
  Subject? _selectedSubject;
  DateTime _selectedDate = DateTime.now();
  List<User> _enrolledStudents = [];
  Map<int, AttendanceStatus> _attendanceMap = {};
  bool _isLoading = true;
  bool _isMarkingAttendance = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadTeacherData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTeacherData() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final teacherId = authProvider.currentUser?.id;

    if (teacherId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final subjects = await _subjectService.getSubjectsByTeacher(teacherId);
      setState(() {
        _teacherSubjects = subjects;
        if (subjects.isNotEmpty && _selectedSubject == null) {
          _selectedSubject = subjects.first;
          _loadEnrolledStudents();
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading teacher data: $e')),
        );
      }
    }
  }

  Future<void> _loadEnrolledStudents() async {
    if (_selectedSubject == null) return;

    try {
      final enrollments = await _subjectService.getSubjectEnrollments(
        _selectedSubject!.id!,
      );
      final studentIds = enrollments.map((e) => e.studentId).toList();

      final students = <User>[];
      for (final studentId in studentIds) {
        final student = await _userService.getUserById(studentId);
        if (student != null) {
          students.add(student);
        }
      }

      // Load existing attendance for the selected date
      final attendanceMap = <int, AttendanceStatus>{};
      for (final student in students) {
        final existingAttendance = await _attendanceService.getAttendanceRecord(
          student.id!,
          _selectedSubject!.id!,
          _selectedDate,
        );
        if (existingAttendance != null) {
          attendanceMap[student.id!] = existingAttendance.status;
        }
      }

      setState(() {
        _enrolledStudents = students;
        _attendanceMap = attendanceMap;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading students: $e')));
      }
    }
  }

  Future<void> _markAttendance() async {
    if (_selectedSubject == null || _enrolledStudents.isEmpty) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final teacherId = authProvider.currentUser?.id;
    if (teacherId == null) return;

    setState(() {
      _isMarkingAttendance = true;
    });

    try {
      final studentIds = _enrolledStudents.map((s) => s.id!).toList();
      final results = await _attendanceService.markBulkAttendance(
        studentIds: studentIds,
        subjectId: _selectedSubject!.id!,
        date: _selectedDate,
        attendanceMap: _attendanceMap,
        markedBy: teacherId,
      );

      final successCount = results.where((r) => r.isSuccess).length;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Attendance marked for $successCount students'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error marking attendance: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isMarkingAttendance = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab Bar
        Container(
          color: Colors.grey[50],
          child: TabBar(
            controller: _tabController,
            labelColor: Colors.green[600],
            unselectedLabelColor: Colors.grey[600],
            indicatorColor: Colors.green[600],
            tabs: const [
              Tab(
                text: 'Mark Attendance',
                icon: Icon(Icons.how_to_reg_outlined),
              ),
              Tab(text: 'View Reports', icon: Icon(Icons.analytics_outlined)),
            ],
          ),
        ),

        // Tab Views
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [_buildMarkAttendanceTab(), _buildReportsTab()],
          ),
        ),
      ],
    );
  }

  Widget _buildMarkAttendanceTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_teacherSubjects.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.book_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No Subjects Assigned',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Contact admin to assign subjects',
              style: TextStyle(color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Subject and Date Selection
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Subject & Date',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Subject Dropdown
                  DropdownButtonFormField<Subject>(
                    value: _selectedSubject,
                    decoration: InputDecoration(
                      labelText: 'Subject',
                      prefixIcon: const Icon(Icons.book_outlined),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    items:
                        _teacherSubjects.map((subject) {
                          return DropdownMenuItem<Subject>(
                            value: subject,
                            child: Text('${subject.name} (${subject.code})'),
                          );
                        }).toList(),
                    onChanged: (Subject? value) {
                      if (value != null) {
                        setState(() {
                          _selectedSubject = value;
                          _attendanceMap.clear();
                        });
                        _loadEnrolledStudents();
                      }
                    },
                  ),
                  const SizedBox(height: 16),

                  // Date Picker
                  InkWell(
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate,
                        firstDate: DateTime.now().subtract(
                          const Duration(days: 30),
                        ),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        setState(() {
                          _selectedDate = date;
                        });
                        _loadEnrolledStudents();
                      }
                    },
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Date',
                        prefixIcon: const Icon(Icons.calendar_today_outlined),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(_formatDate(_selectedDate)),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Students List
          if (_selectedSubject != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Students (${_enrolledStudents.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          for (final student in _enrolledStudents) {
                            _attendanceMap[student.id!] =
                                AttendanceStatus.present;
                          }
                        });
                      },
                      icon: const Icon(Icons.check_circle, color: Colors.green),
                      label: const Text('All Present'),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          for (final student in _enrolledStudents) {
                            _attendanceMap[student.id!] =
                                AttendanceStatus.absent;
                          }
                        });
                      },
                      icon: const Icon(Icons.cancel, color: Colors.red),
                      label: const Text('All Absent'),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),

            if (_enrolledStudents.isEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Icon(
                        Icons.people_outline,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No Students Enrolled',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Students need to enroll in this subject',
                        style: TextStyle(color: Colors.grey[500]),
                      ),
                    ],
                  ),
                ),
              )
            else
              ..._enrolledStudents.map(
                (student) => _buildStudentAttendanceCard(student),
              ),

            const SizedBox(height: 24),

            // Mark Attendance Button
            if (_enrolledStudents.isNotEmpty)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isMarkingAttendance ? null : _markAttendance,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child:
                      _isMarkingAttendance
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Text(
                            'Mark Attendance',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildStudentAttendanceCard(User student) {
    final currentStatus = _attendanceMap[student.id];

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Student Avatar
            CircleAvatar(
              backgroundColor: Colors.blue[100],
              child: Text(
                student.name.isNotEmpty ? student.name[0].toUpperCase() : 'S',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[600],
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Student Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    student.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    'ID: ${student.id}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
            ),

            // Attendance Buttons
            Row(
              children: [
                // Present Button
                InkWell(
                  onTap: () {
                    setState(() {
                      _attendanceMap[student.id!] = AttendanceStatus.present;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          currentStatus == AttendanceStatus.present
                              ? Colors.green
                              : Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.green, width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check,
                          size: 16,
                          color:
                              currentStatus == AttendanceStatus.present
                                  ? Colors.white
                                  : Colors.green,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Present',
                          style: TextStyle(
                            color:
                                currentStatus == AttendanceStatus.present
                                    ? Colors.white
                                    : Colors.green,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 8),

                // Absent Button
                InkWell(
                  onTap: () {
                    setState(() {
                      _attendanceMap[student.id!] = AttendanceStatus.absent;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          currentStatus == AttendanceStatus.absent
                              ? Colors.red
                              : Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.red, width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.close,
                          size: 16,
                          color:
                              currentStatus == AttendanceStatus.absent
                                  ? Colors.white
                                  : Colors.red,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Absent',
                          style: TextStyle(
                            color:
                                currentStatus == AttendanceStatus.absent
                                    ? Colors.white
                                    : Colors.red,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.analytics_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Attendance Reports',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Detailed reports coming soon!',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
